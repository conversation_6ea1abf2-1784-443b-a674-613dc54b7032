package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/sof/paginated"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiStatementOfFactsV2PaginatedController(
    private val visitV2Service: VisitV2Service,
    private val esofV2Service: EsofV2Service,
    private val shipService: StaticShipInfoService
) {

    @GetMapping("/all")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getAllStatementOfFactsViews(
        @RequestParam("view") view: StatementOfFactsViewName,
        @RequestParam("page") page: Int,
        @RequestParam("pageSize") pageSize: Int = 100,
        @RequestParam("categories") categories: Set<ShipCategoryV2>? = emptySet(),
        @RequestParam("start") start: Instant? = null,
        @RequestParam("end") end: Instant? = null,
        @RequestParam("finished") finished: Boolean? = null,
        @RequestParam("confirmed") confirmed: Boolean? = null
    ): PaginatedStatementOfFactsViewResponse {
        validatePaginationParameters(page, pageSize)
        
        val request = AllStatementOfFactsViewRequest(
            view = view,
            page = page,
            pageSize = pageSize,
            categories = categories,
            start = start,
            end = end,
            finished = finished,
            confirmed = confirmed
        )
        
        return getAllStatementOfFactsViews(request)
    }

    @GetMapping("/all/count")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getAllStatementOfFactsViewsCount(
        @RequestParam("categories") categories: Set<ShipCategoryV2>? = emptySet(),
        @RequestParam("start") start: Instant? = null,
        @RequestParam("end") end: Instant? = null,
        @RequestParam("finished") finished: Boolean? = null,
        @RequestParam("confirmed") confirmed: Boolean? = null
    ): Long {
        val qualifyingImos = selectImosOrEmpty(categories)
        
        return if (start != null || end != null || finished != null || confirmed != null) {
            // For filtered queries, we need to count visits that match the criteria
            // This is a simplified approach - in a real implementation you might want
            // to add a dedicated count method to the data source
            val visits = findVisitsForAllQuery(
                qualifyingImos = qualifyingImos,
                start = start,
                end = end,
                finished = finished,
                confirmed = confirmed,
                limit = Int.MAX_VALUE
            )
            visits.size.toLong()
        } else {
            // For unfiltered queries, use the existing limited count method
            visitV2Service.countByLimited(limited = false, imos = qualifyingImos)
        }
    }

    private fun getAllStatementOfFactsViews(request: AllStatementOfFactsViewRequest): PaginatedStatementOfFactsViewResponse {
        val qualifyingImos = selectImosOrEmpty(request.categories)
        
        val visits = if (request.hasTimeOrStateFilters()) {
            // Use filtered query for time/state based requests
            findVisitsForAllQuery(
                qualifyingImos = qualifyingImos,
                start = request.start,
                end = request.end,
                finished = request.finished,
                confirmed = request.confirmed,
                limit = request.pageSize,
                skip = (request.page - 1) * request.pageSize
            )
        } else {
            // Use the existing limited pagination for simple requests
            visitV2Service.findByLimited(
                limited = false,
                imos = qualifyingImos,
                page = request.page,
                pageSize = request.pageSize
            )
        }
        
        val sofs = esofV2Service.produceBulk(request.view, visits)
        
        return PaginatedStatementOfFactsViewResponse(
            request = request,
            data = sofs,
            totalCount = null // Could be implemented if needed
        )
    }

    private fun findVisitsForAllQuery(
        qualifyingImos: Set<Int>,
        start: Instant?,
        end: Instant?,
        finished: Boolean?,
        confirmed: Boolean?,
        limit: Int,
        skip: Int = 0
    ): List<nl.teqplay.vesselvoyage.model.v2.NewVisit> {
        // This is a simplified implementation. In a real scenario, you would need to add
        // a new method to NewVisitDataSource that supports pagination with time/state filters
        // For now, we'll use a basic approach
        
        val finishedState = nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ofBoolean(finished)
        
        // This is a placeholder - you would need to implement a proper paginated query
        // in the data source layer that supports all these filters together
        throw BadRequestException("Time and state filtered pagination not yet implemented. Please use the existing /byPort or /byImo endpoints for filtered queries.")
    }

    private fun selectImosOrEmpty(categories: Set<ShipCategoryV2>?): Set<Int> {
        return if (categories.isNullOrEmpty()) {
            emptySet()
        } else {
            shipService.getImosByCategories(categories)
        }
    }

    private fun validatePaginationParameters(page: Int, pageSize: Int) {
        if (page < 1) {
            throw BadRequestException("Page number must be greater than or equal to 1")
        }
        if (pageSize < 1 || pageSize > 1000) {
            throw BadRequestException("Page size must be between 1 and 1000")
        }
    }

    data class AllStatementOfFactsViewRequest(
        val view: StatementOfFactsViewName,
        val page: Int,
        val pageSize: Int,
        val categories: Set<ShipCategoryV2>?,
        val start: Instant?,
        val end: Instant?,
        val finished: Boolean?,
        val confirmed: Boolean?
    ) {
        fun hasTimeOrStateFilters(): Boolean {
            return start != null || end != null || finished != null || confirmed != null
        }
    }

    data class PaginatedStatementOfFactsViewResponse(
        val request: AllStatementOfFactsViewRequest,
        val data: List<StatementOfFactsView>,
        val totalCount: Long?
    )
}
